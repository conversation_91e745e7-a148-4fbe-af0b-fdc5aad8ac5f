import {
  IconBarrierBlock,
  IconBrowserCheck,
  IconBug,
  IconError404,
  IconHelp,
  IconLayoutDashboard,
  IconLock,
  IconLockAccess,
  IconNotification,
  IconPalette,
  IconServerOff,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUserOff,
  IconUsers,
  IconServer,
  IconCloud,
  IconChartBar,
  IconCreditCard,
  IconCluster,
  IconApi,
} from '@tabler/icons-react'
import { AudioWaveform, Command, GalleryVerticalEnd } from 'lucide-react'
import { type SidebarData } from '../types'

export const sidebarData: SidebarData = {
  user: {
    name: 'Enterprise Admin',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'EdgeOpenAPI',
      logo: Command,
      plan: 'Enterprise Gateway',
    },
    {
      name: 'Production',
      logo: GalleryVerticalEnd,
      plan: 'Live Environment',
    },
    {
      name: 'Development',
      logo: AudioWaveform,
      plan: 'Test Environment',
    },
  ],
  navGroups: [
    {
      title: 'Overview',
      items: [
        {
          title: 'Dashboard',
          url: '/',
          icon: IconLayoutDashboard,
        },
        {
          title: 'API Documentation',
          url: '/api-docs',
          icon: IconApi,
        },
      ],
    },
    {
      title: 'User Management',
      items: [
        {
          title: 'Users',
          url: '/users',
          icon: IconUsers,
        },
      ],
    },
    {
      title: 'Infrastructure',
      items: [
        {
          title: 'Clusters',
          url: '/clusters',
          icon: IconCluster,
        },
        {
          title: 'Nodes',
          url: '/nodes',
          icon: IconServer,
        },
      ],
    },
    {
      title: 'Services',
      items: [
        {
          title: 'CDN Services',
          url: '/services',
          icon: IconCloud,
        },
      ],
    },
    {
      title: 'Analytics & Billing',
      items: [
        {
          title: 'Statistics',
          url: '/statistics',
          icon: IconChartBar,
        },
        {
          title: 'Billing',
          url: '/billing',
          icon: IconCreditCard,
        },
      ],
    },
    {
      title: 'System',
      items: [
        {
          title: 'Settings',
          icon: IconSettings,
          items: [
            {
              title: 'Profile',
              url: '/settings',
              icon: IconUserCog,
            },
            {
              title: 'Account',
              url: '/settings/account',
              icon: IconTool,
            },
            {
              title: 'Appearance',
              url: '/settings/appearance',
              icon: IconPalette,
            },
            {
              title: 'Notifications',
              url: '/settings/notifications',
              icon: IconNotification,
            },
            {
              title: 'Display',
              url: '/settings/display',
              icon: IconBrowserCheck,
            },
          ],
        },
        {
          title: 'Help Center',
          url: '/help-center',
          icon: IconHelp,
        },
      ],
    },
  ],
}
